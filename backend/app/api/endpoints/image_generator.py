"""
Image Generator API endpoints using Ideogram AI v3.
Based on posters.py but adapted for general image generation without prompt modifications.
"""

from fastapi import APIRouter, Form, File, UploadFile, HTTPException
from typing import List, Optional
import logging

from app.services.image_generator_service import image_generator_service
from app.schemas.poster import FrontendPosterResponse

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter(
    tags=["image-generator"],
    responses={
        404: {"description": "Not found"},
        500: {"description": "Internal server error"}
    }
)


@router.post("/generate")
async def generate_image(
    prompt: str = Form(..., description="Description of the image to create"),
    resolution: Optional[str] = Form(None, description="Ideogram resolution (e.g., '1024x1024')"),
    aspect_ratio: Optional[str] = Form(None, description="Aspect ratio (e.g., '1:1')"),
    rendering_speed: str = Form(default="DEFAULT", description="Rendering speed: TURBO/DEFAULT/QUALITY"),
    magic_prompt: str = Form(default="AUTO", description="Magic prompt: AUTO/ON/OFF"),
    negative_prompt: Optional[str] = Form(None, description="What to exclude from image"),
    num_images: int = Form(default=1, description="Number of images to generate (1-8)"),
    style_type: str = Form(default="GENERAL", description="Style type: AUTO/GENERAL/REALISTIC/DESIGN")
) -> FrontendPosterResponse:
    """Generate an image using Ideogram AI v3 model."""

    try:
        logger.info(f"🎨 Generating image with Ideogram: {prompt[:100]}...")

        # Call the service with all parameters
        service_response = await image_generator_service.generate_image(
            prompt=prompt,
            resolution=resolution,
            aspect_ratio=aspect_ratio,
            rendering_speed=rendering_speed,
            magic_prompt=magic_prompt,
            negative_prompt=negative_prompt,
            num_images=num_images,
            style_type=style_type
        )

        # Convert to frontend response
        return FrontendPosterResponse.from_service_response(service_response)
        
    except Exception as e:
        logger.error(f"Error in generate_image endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during image generation: {e}"
        )


@router.post("/multi-turn-edit")
async def multi_turn_edit_image(
    previous_response_id: str = Form(..., description="ID of the previous response to build upon"),
    edit_prompt: str = Form(..., description="Description of the changes to make")
) -> FrontendPosterResponse:
    """Edit an existing image using multi-turn generation."""
    
    try:
        logger.info(f"🔄 Multi-turn editing image: {edit_prompt[:100]}...")
        
        # Call the service
        service_response = await image_generator_service.multi_turn_edit(
            previous_response_id=previous_response_id,
            edit_prompt=edit_prompt
        )
        
        # Convert to frontend response
        return FrontendPosterResponse.from_service_response(service_response)
        
    except Exception as e:
        logger.error(f"Error in multi_turn_edit_image endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during multi-turn editing: {e}"
        )


@router.post("/edit-with-references")
async def edit_image_with_references(
    prompt: str = Form(..., description="Description of the image to create"),
    reference_images: List[UploadFile] = File(..., description="Reference images (max 4)"),
    resolution: Optional[str] = Form(None, description="Ideogram resolution (e.g., '1024x1024')"),
    aspect_ratio: Optional[str] = Form(None, description="Aspect ratio (e.g., '1:1')")
) -> FrontendPosterResponse:
    """Generate image using reference images."""
    
    try:
        logger.info(f"🖼️ Generating image using {len(reference_images)} references: {prompt[:100]}...")
        
        # Validate reference images count
        if len(reference_images) > 4:
            raise HTTPException(
                status_code=400,
                detail="Maximum 4 reference images allowed"
            )
        
        # Call the service
        service_response = await image_generator_service.edit_with_references(
            prompt=prompt,
            reference_images=reference_images,
            resolution=resolution,
            aspect_ratio=aspect_ratio
        )
        
        # Convert to frontend response
        return FrontendPosterResponse.from_service_response(service_response)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in edit_image_with_references endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during reference-based generation: {e}"
        )


@router.post("/edit-with-mask")
async def edit_image_with_mask(
    prompt: str = Form(..., description="Description of the changes to make"),
    image: UploadFile = File(..., description="Original image file"),
    mask: UploadFile = File(..., description="Mask image file (white areas will be edited)")
) -> FrontendPosterResponse:
    """Edit image using mask-based editing."""
    
    try:
        logger.info(f"🎭 Editing image using mask: {prompt[:100]}...")
        
        # Call the service
        service_response = await image_generator_service.edit_with_mask(
            prompt=prompt,
            image=image,
            mask=mask
        )
        
        # Convert to frontend response
        return FrontendPosterResponse.from_service_response(service_response)
        
    except Exception as e:
        logger.error(f"Error in edit_image_with_mask endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during mask-based editing: {e}"
        )


@router.get("/health")
async def health_check():
    """Health check endpoint for image generator service."""
    return {
        "status": "healthy",
        "service": "image-generator",
        "message": "Image Generator service is running"
    }
