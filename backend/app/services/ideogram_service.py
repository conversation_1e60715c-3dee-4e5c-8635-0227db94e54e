"""
Ideogram.ai Service - Specialized service for generating advertisements using Ideogram.ai
Perfect for text-heavy marketing materials with professional quality at low cost.
"""

import logging
import httpx
import base64
import os
from typing import Dict, Any, List
from fastapi import UploadFile
from app.core.config import settings

logger = logging.getLogger(__name__)


class IdeogramService:
    """Service for generating advertisements using Ideogram.ai - optimized for text and marketing."""
    
    def __init__(self):
        """Initialize the Ideogram service."""
        self.api_key = os.getenv("IDEOGRAM_API_KEY") or settings.IDEOGRAM_API_KEY
        self.base_url = "https://api.ideogram.ai/v1"
        
    async def generate_ad(self, prompt: str, size: str = "1024x1024") -> Dict[str, Any]:
        """
        Generate an advertisement using Ideogram.ai.
        
        Args:
            prompt: Description of the advertisement to create
            size: Image size (1024x1024, 1024x1792, 1792x1024)
            
        Returns:
            Dict with success status, image data, and metadata
        """
        if not self.api_key:
            logger.error("Ideogram API key not configured")
            return {"success": False, "error": "Ideogram API key not configured"}
        
        try:
            # Enhanced prompt specifically for Ideogram.ai's strengths
            enhanced_prompt = f"""Create a professional, high-quality advertisement image with the following concept: {prompt}

VISUAL REQUIREMENTS:
- Professional agency-level production quality
- Clean, modern composition with strategic product placement
- High-resolution commercial photography aesthetic
- Professional lighting and shadows
- Premium brand appearance suitable for marketing campaigns

TEXT INTEGRATION REQUIREMENTS:
- All text elements must be clearly readable and well-integrated
- Use professional typography with proper hierarchy
- Ensure text contrasts well with background elements
- Text should feel naturally part of the design, not overlaid
- Maintain brand-appropriate font styling

TECHNICAL SPECIFICATIONS:
- Ultra-high resolution appearance
- Professional color grading and balance
- Sharp focus on main product/subject
- Balanced composition following design principles
- Commercial photography standards
- Print and digital ready quality

The final image should look like it was created by a top-tier advertising agency for a major brand campaign."""

            # Map common sizes to Ideogram format
            size_mapping = {
                "1024x1024": "SQUARE",
                "1024x1792": "PORTRAIT", 
                "1792x1024": "LANDSCAPE",
                "auto": "SQUARE"
            }
            
            ideogram_size = size_mapping.get(size, "SQUARE")
            
            payload = {
                "image_request": {
                    "model": "V_3_0_QUALITY",  # Ideogram 3.0 Quality - Best model for professional ads
                    "prompt": enhanced_prompt,
                    "aspect_ratio": ideogram_size,
                    "magic_prompt_option": "AUTO",  # Let Ideogram enhance the prompt
                    "seed": None,  # Random seed for variety
                    "style_type": "GENERAL",  # General style for versatility
                    "negative_prompt": "blurry, low quality, pixelated, amateur, unprofessional, poor lighting, bad composition, illegible text, watermark, low resolution"
                }
            }
            
            headers = {
                "Api-Key": self.api_key,
                "Content-Type": "application/json"
            }
            
            logger.info(f"🎨 Generating advertisement with Ideogram.ai: {prompt[:100]}...")
            
            async with httpx.AsyncClient(timeout=120.0) as client:
                response = await client.post(
                    f"{self.base_url}/generate",
                    json=payload,
                    headers=headers
                )
                
                if response.status_code != 200:
                    error_text = response.text
                    logger.error(f"Ideogram error {response.status_code}: {error_text}")
                    return {"success": False, "error": f"Ideogram error: {error_text}"}
                
                result = response.json()
                
                # Ideogram response format
                if "data" in result and len(result["data"]) > 0:
                    image_data = result["data"][0]
                    image_url = image_data.get("url")
                    
                    if image_url:
                        return {
                            "success": True,
                            "image_url": image_url,
                            "revised_prompt": image_data.get("prompt"),
                            "metadata": {
                                "model": "ideogram-3.0-quality",
                                "size": size,
                                "aspect_ratio": ideogram_size,
                                "original_prompt": prompt,
                                "enhanced_prompt": enhanced_prompt,
                                "seed": image_data.get("seed"),
                                "is_image_safe": image_data.get("is_image_safe", True)
                            }
                        }
                    else:
                        logger.error(f"No image URL found in response: {image_data}")
                        return {"success": False, "error": "No image URL in response"}
                else:
                    logger.error(f"No data array in response: {result}")
                    return {"success": False, "error": "No image data in response"}
                    
        except Exception as e:
            logger.error(f"Error generating advertisement with Ideogram: {e}")
            return {"success": False, "error": f"Error: {str(e)}"}

    async def generate_with_reference(self, prompt: str, reference_image: UploadFile, size: str = "1024x1024") -> Dict[str, Any]:
        """
        Generate advertisement using a reference image with Ideogram.ai.
        
        Args:
            prompt: Description of the advertisement to create
            reference_image: Reference image to guide the generation
            size: Image size
            
        Returns:
            Dict with success status, image data, and metadata
        """
        if not self.api_key:
            logger.error("Ideogram API key not configured")
            return {"success": False, "error": "Ideogram API key not configured"}
        
        try:
            # Read and encode reference image
            image_content = await reference_image.read()
            base64_image = base64.b64encode(image_content).decode("utf-8")
            
            # Enhanced prompt for reference-based generation
            enhanced_prompt = f"""Create a professional advertisement inspired by the reference image style with this concept: {prompt}

STYLE ADAPTATION:
- Adapt the visual style, composition, and aesthetic from the reference image
- Maintain the professional quality and commercial appeal
- Integrate text elements naturally as shown in reference
- Use similar lighting, color palette, and mood
- Keep the same level of sophistication and brand appeal

TEXT REQUIREMENTS:
- Ensure all text is clearly readable and professionally integrated
- Match the typography style and placement approach from reference
- Maintain proper text hierarchy and contrast

The result should feel cohesive with the reference while being unique and professional."""

            size_mapping = {
                "1024x1024": "SQUARE",
                "1024x1792": "PORTRAIT", 
                "1792x1024": "LANDSCAPE",
                "auto": "SQUARE"
            }
            
            ideogram_size = size_mapping.get(size, "SQUARE")
            
            payload = {
                "image_request": {
                    "model": "V_3_0_QUALITY",  # Ideogram 3.0 Quality for reference-based generation
                    "prompt": enhanced_prompt,
                    "aspect_ratio": ideogram_size,
                    "magic_prompt_option": "AUTO",
                    "style_type": "GENERAL",
                    "negative_prompt": "blurry, low quality, pixelated, amateur, unprofessional, poor lighting, bad composition, illegible text, low resolution",
                    "image_file": f"data:image/png;base64,{base64_image}"
                }
            }
            
            headers = {
                "Api-Key": self.api_key,
                "Content-Type": "application/json"
            }
            
            logger.info(f"🖼️ Generating with reference using Ideogram.ai: {prompt[:100]}...")
            
            async with httpx.AsyncClient(timeout=120.0) as client:
                response = await client.post(
                    f"{self.base_url}/generate",
                    json=payload,
                    headers=headers
                )
                
                if response.status_code != 200:
                    error_text = response.text
                    logger.error(f"Ideogram reference error {response.status_code}: {error_text}")
                    return {"success": False, "error": f"Ideogram error: {error_text}"}
                
                result = response.json()
                
                if "data" in result and len(result["data"]) > 0:
                    image_data = result["data"][0]
                    image_url = image_data.get("url")
                    
                    if image_url:
                        return {
                            "success": True,
                            "image_url": image_url,
                            "revised_prompt": image_data.get("prompt"),
                            "metadata": {
                                "model": "ideogram-3.0-quality",
                                "size": size,
                                "aspect_ratio": ideogram_size,
                                "original_prompt": prompt,
                                "enhanced_prompt": enhanced_prompt,
                                "seed": image_data.get("seed"),
                                "type": "reference_based",
                                "reference_used": True,
                                "is_image_safe": image_data.get("is_image_safe", True)
                            }
                        }
                    else:
                        return {"success": False, "error": "No image URL in reference response"}
                else:
                    return {"success": False, "error": "No image data in reference response"}
                    
        except Exception as e:
            logger.error(f"Error in reference generation with Ideogram: {e}")
            return {"success": False, "error": f"Error: {str(e)}"}


# Global service instance
ideogram_service = IdeogramService()
