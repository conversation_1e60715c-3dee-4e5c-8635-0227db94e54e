import React from 'react';
import { DashboardLayout } from '@/components/layout/dashboard-layout';

const featuredAgents = [
  {
    name: '🔍 Hunter Pro (Scraper + Outreach)',
    description: 'Encuentra correos en sitios web, escribe por ti y lanza campañas frías personalizadas. Automatiza la prospección. Ideal para conseguir nuevos clientes sin esfuerzo.',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuAhccyL3ppX44h9JAaS1SjAN80T30tVSyKuKiNmoxfJfm_O-1GujXvl6y5zlj7mPFUjgZbRtxJQ8ROrtEAJuJZ_LwmjMunF3TrCJze6Yw-5PMZ4VHr5aigK12ABtgjc4ZP6kuzKuLwBBTmIyzH81i57X0yyWkSRFF5y1YNUJxHGGnfzSgu3HF_xywsbNaGMvMcQ-6a3gep_8Hglj2OQk8JkJxzSrh6Fc756zsN1vYvoDF8ktfLDgy-WonYlhmwKoVUv7YhLh3QsYKe3'
  },
  {
    name: '📆 Calendar Bot (Agendador de Reuniones)',
    description: 'Recibe leads, califica y agenda reuniones automáticamente en tu calendario. Siempre ocupado, nunca confundido.',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuDJAUkiu2_e6WwAwi6u6qVgMmmwp4mFZjDDsdMXqnv02JYyslA_lo60JWTlCbbUd8xscMiiIM0yHMMhJHeYWtzK-dyWA__d_35FHT2zQEsEL6mHTP_CuBe_pDd2rHa6uQKgpqb4Fu-AZjfxZJPQMOhCe-s_K74EWSiUCIzR18dcKLFq9cobeUMqWEbV7uPv25rq3A4sVAispq-3vHKyXnTQOCbAe0VFLLK29LTfd45cunFqc9lbUVt01i-j4XTfljPJlDCIr4_5-S3I'
  },
  {
    name: '💬 Community Manager AI',
    description: 'Responde en tus redes, nutre la comunidad, da soporte y mantiene vivas tus cuentas 24/7. Un CM que nunca duerme ni se equivoca.',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuAzfwej9q5b6fnve5coRETKbOn4OoKDdbBfKRpCLevpPAi2wq0IxeQ0AbvHMGPBG7CoMTxTMWysCZUVeMEuu9S2T5yIzaI_PWwDyMIzwpY21gA4WOWzcng_B1aOQMfqqjayp6EVUNSOi624KPG8sKU6-QwxlVHEhE2rx_fnw47YLtSZ7d2lBHZE-CVnUpnyf4IEkD5p6URorJwRKER5RJEuFF9dyN8_Rg4e_XifN2Cl_Ks91tsjQO0XDur1JOWNXdtt0yv9HKZVY55u'
  },
  {
    name: '📲 Lead Agent (WhatsApp SDR)',
    description: 'Recibe leads en caliente, conversa, pre-califica, responde objeciones y avanza el cierre. Todo desde WhatsApp, en automático.',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuAhccyL3ppX44h9JAaS1SjAN80T30tVSyKuKiNmoxfJfm_O-1GujXvl6y5zlj7mPFUjgZbRtxJQ8ROrtEAJuJZ_LwmjMunF3TrCJze6Yw-5PMZ4VHr5aigK12ABtgjc4ZP6kuzKuLwBBTmIyzH81i57X0yyWkSRFF5y1YNUJxHGGnfzSgu3HF_xywsbNaGMvMcQ-6a3gep_8Hglj2OQk8JkJxzSrh6Fc756zsN1vYvoDF8ktfLDgy-WonYlhmwKoVUv7YhLh3QsYKe3'
  },
  {
    name: '☎️ Sales Support (Inbound + Cold Call)',
    description: 'Atiende llamadas entrantes y lanza llamadas salientes preprogramadas. Ideal para atención al cliente y prospección automatizada.',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuDJAUkiu2_e6WwAwi6u6qVgMmmwp4mFZjDDsdMXqnv02JYyslA_lo60JWTlCbbUd8xscMiiIM0yHMMhJHeYWtzK-dyWA__d_35FHT2zQEsEL6mHTP_CuBe_pDd2rHa6uQKgpqb4Fu-AZjfxZJPQMOhCe-s_K74EWSiUCIzR18dcKLFq9cobeUMqWEbV7uPv25rq3A4sVAispq-3vHKyXnTQOCbAe0VFLLK29LTfd45cunFqc9lbUVt01i-j4XTfljPJlDCIr4_5-S3I'
  }
];

const testimonials = [
  {
    name: 'Sophia Lin, Marketing Manager',
    content: 'Los agentes de Emma han revolucionado nuestro soporte al cliente. El agente de IA maneja la mayoría de las consultas, liberando a nuestro equipo para enfocarse en problemas complejos.',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuAKfkwPNMGcrBDkFdYkqXQIfmfRzafA0OalZqn75er8pEI0idQHBDHJrpEhI8bTFQYqwCca95waICoA4kpZH-n-5x22z8ymk-XW4PljRfmR6DS2ouUn9B_wOKtAN9aHFm-pdWk8X9puJT00URBRC1lAiTkPn1eoF7_CXQSRArHWk9iaipxetNPPmzCyCLWXL7-USFZyIokUrxlFusT-TT0t9kbVbsxpThMGLPWE2-4GDmot0F9DsuZ39cvpFoKS0c50xmivUfI85Pow'
  },
  {
    name: 'Ethan Wong, Business Analyst',
    content: 'El agente de análisis de datos ha proporcionado información invaluable sobre el rendimiento de nuestro negocio, ayudándonos a identificar áreas clave de mejora.',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuDtTFaRgM7cGiXBAiRYDeSEy0oAxnLBQT0kcH0dXKZSY5CsY5Hb4Tb-1aYf_FIlH45yQbDVGmLLxE3IzOBOs-8EG_yAHGwAGK0Yxy3WpsuUhoILD8Au9TIjKIhWQE8pCpblvv0k_LJafVvB4jgjdPB08tQ9hVQgpoJ6EcG_9UIPhd-5PNod_znAHQn3GuGQLjkZRfKZCL0ve3Cp1k-71Omju0BxXcctANpoxkZVB-tsR2Bn2ZaTfUyL8EMJnEjeOfUxk90p39A5SKqm'
  },
  {
    name: 'Chloe Tan, Content Creator',
    content: 'Ahora puedo crear contenido atractivo en una fracción del tiempo, gracias al agente de creación de contenido. Es un cambio total!',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuDSSz-lEzGaRBUbdbAKiWSrRcMss8PBMPKJUVOyYGeO0aorZw2XAMQgZ5ZYIf_aWwLgG8xrKh-y9aiXBeAb0Nkw5ZlFQhOH240g09yJdfLFP6exm6NpXdPwTl2aw6Hj6YFsoe5HlQRr88mmerea-AMBtIwx8YaXCV3hDrW0OUXH3BQes88zxWmbydKWMrBo_2JY6flUwrDQMXUXSTe6gOsadTHNmXzcXRLZNjOn1xxj_kV574kLeTxh6xsLq9mWzkBdDlSjwQ4MJJSA'
  }
];

export default function AgentsMarketplacePage() {
  return (
    <DashboardLayout pageTitle="Marketplace de Agentes IA">
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-purple-50">
        {/* Hero Section - Emma Style */}
        <div className="relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-[#3018ef]/10 via-transparent to-[#dd3a5a]/10"></div>
          <div className="relative px-6 py-12 max-w-7xl mx-auto">
            <div className="text-center mb-12">
              <div className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-gradient-to-br from-[#3018ef] to-[#dd3a5a] mb-6 shadow-lg">
                <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
              </div>
              <h1 className="text-4xl md:text-6xl font-black bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent mb-4">
                Marketplace de Agentes IA
              </h1>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
                Descubre y despliega agentes de IA especializados diseñados para automatizar tareas, 
                aumentar la productividad e impulsar la innovación en tu negocio.
              </p>
              <button className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] text-white px-8 py-4 rounded-full font-bold text-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300">
                <span className="flex items-center gap-2">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.516 6.516 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5z"/>
                  </svg>
                  Explorar Agentes
                </span>
              </button>
            </div>
          </div>
        </div>

        {/* Agentes Emma Section */}
        <div className="px-6 py-12 max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
              ⚙️ Agentes Emma – Edición Plug & Profit 💰
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Agentes especializados listos para generar ingresos inmediatos en tu negocio
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {featuredAgents.map((agent, index) => (
              <div key={index} className="group relative">
                <div className="relative bg-white/70 backdrop-blur-lg rounded-2xl p-6 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 hover:scale-105">
                  <div className="absolute inset-0 bg-gradient-to-r from-[#3018ef]/20 to-[#dd3a5a]/20 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"></div>
                  
                  <div className="relative mb-6">
                    <div
                      className="w-full aspect-square bg-cover bg-center rounded-xl shadow-lg"
                      style={{backgroundImage: `url("${agent.image}")`}}
                    ></div>
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-xl"></div>
                  </div>
                  
                  <div className="space-y-4">
                    <h3 className="text-xl font-bold text-gray-800 group-hover:bg-gradient-to-r group-hover:from-[#3018ef] group-hover:to-[#dd3a5a] group-hover:bg-clip-text group-hover:text-transparent transition-all duration-300">
                      {agent.name}
                    </h3>
                    <p className="text-gray-600 leading-relaxed">
                      {agent.description}
                    </p>
                    
                    <button className="w-full bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] text-white py-3 px-6 rounded-xl font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300">
                      <span className="flex items-center justify-center gap-2">
                        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                        </svg>
                        Activar Agente
                      </span>
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Testimonials Section */}
        <div className="px-6 py-12 max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
              Lo que dicen nuestros usuarios
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Testimonios reales de empresas que han transformado su productividad con Emma
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="group relative">
                <div className="relative bg-white/70 backdrop-blur-lg rounded-2xl p-6 shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300">
                  <div className="absolute top-4 right-4 w-8 h-8 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] rounded-full flex items-center justify-center">
                    <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M14,17H17L19,13V7H13V13H16M6,17H9L11,13V7H5V13H8L6,17Z"/>
                    </svg>
                  </div>
                  
                  <div className="relative mb-6">
                    <div
                      className="w-20 h-20 bg-cover bg-center rounded-full shadow-lg mx-auto border-4 border-white"
                      style={{backgroundImage: `url("${testimonial.image}")`}}
                    ></div>
                  </div>
                  
                  <div className="text-center space-y-4">
                    <p className="text-gray-700 italic leading-relaxed">
                      "{testimonial.content}"
                    </p>
                    <div>
                      <p className="font-bold text-gray-800">{testimonial.name}</p>
                      <div className="flex justify-center mt-2">
                        {[...Array(5)].map((_, i) => (
                          <svg key={i} className="w-4 h-4 text-yellow-400 fill-current" viewBox="0 0 24 24">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                          </svg>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="relative px-6 py-16 max-w-7xl mx-auto">
          <div className="relative bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] rounded-3xl p-12 text-center overflow-hidden">
            <div className="absolute inset-0 opacity-10">
              <div className="absolute top-0 left-0 w-32 h-32 bg-white rounded-full -translate-x-16 -translate-y-16"></div>
              <div className="absolute bottom-0 right-0 w-40 h-40 bg-white rounded-full translate-x-20 translate-y-20"></div>
              <div className="absolute top-1/2 left-1/4 w-24 h-24 bg-white rounded-full"></div>
            </div>
            
            <div className="relative z-10">
              <h2 className="text-3xl md:text-5xl font-black text-white mb-6">
                ¿Listo para Transformar tu Negocio?
              </h2>
              <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
                Únete a miles de empresas que ya están usando los agentes de Emma para impulsar su productividad y generar más ingresos.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <button className="bg-white text-[#3018ef] px-8 py-4 rounded-full font-bold text-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300">
                  <span className="flex items-center gap-2">
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M8,5.14V19.14L19,12.14L8,5.14Z"/>
                    </svg>
                    Comenzar Ahora
                  </span>
                </button>
                <button className="border-2 border-white text-white px-8 py-4 rounded-full font-bold text-lg hover:bg-white hover:text-[#3018ef] transition-all duration-300">
                  <span className="flex items-center gap-2">
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,17L7,12L8.41,10.59L12,14.17L15.59,10.59L17,12L12,17Z"/>
                    </svg>
                    Ver Demo
                  </span>
                </button>
              </div>
              
              <div className="mt-8 flex justify-center items-center gap-8 text-white/80">
                <div className="flex items-center gap-2">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M11,16.5L18,9.5L16.59,8.09L11,13.67L7.41,10.09L6,11.5L11,16.5Z"/>
                  </svg>
                  <span>Sin configuración compleja</span>
                </div>
                <div className="flex items-center gap-2">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M11,16.5L18,9.5L16.59,8.09L11,13.67L7.41,10.09L6,11.5L11,16.5Z"/>
                  </svg>
                  <span>Resultados inmediatos</span>
                </div>
                <div className="flex items-center gap-2">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M11,16.5L18,9.5L16.59,8.09L11,13.67L7.41,10.09L6,11.5L11,16.5Z"/>
                  </svg>
                  <span>Soporte 24/7</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
