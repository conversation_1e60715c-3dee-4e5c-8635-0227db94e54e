/**
 * Ad Creator Editor Page - Simplified Results-Focused Flow
 */

import { useRoute } from "wouter";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";

// Import our simplified ad creator
import { SimplifiedAdCreator } from "@/components/tools/ad-creator/shared/SimplifiedAdCreator";
import { PlatformConfig } from "@/types/ad-creator-types";

type PlatformKey = "facebook" | "instagram" | "instagram-stories" | "google" | "linkedin" | "youtube" | "display";

// Platform configurations
const PLATFORM_CONFIGS: Record<PlatformKey, PlatformConfig> = {
  facebook: {
    name: "Facebook",
    icon: "📘",
    color: "#1877F2",
    promptPrefix: "Create a professional Facebook ad for: ",
    defaultSize: "1080x1080",
    sizes: ["1080x1080", "1200x628", "1080x1920"],
    description: "Crea anuncios profesionales para Facebook",
    specifications: {
      aspectRatio: "1:1, 16:9, 9:16",
      fileSize: "Máximo 30MB",
      fileTypes: ["JPG", "PNG", "GIF"],
      textLimit: "125 caracteres recomendados",
      guidelines: ["Evita más del 20% de texto", "Usa imágenes de alta calidad", "Incluye llamada a la acción clara"]
    }
  },
  instagram: {
    name: "Instagram",
    icon: "📷",
    color: "#E4405F",
    promptPrefix: "Create a trendy Instagram ad for: ",
    defaultSize: "1080x1080",
    sizes: ["1080x1080", "1080x1920"],
    description: "Crea anuncios atractivos para Instagram",
    specifications: {
      aspectRatio: "1:1, 9:16",
      fileSize: "Máximo 30MB",
      fileTypes: ["JPG", "PNG"],
      textLimit: "125 caracteres recomendados",
      guidelines: ["Usa colores vibrantes", "Incluye hashtags relevantes", "Optimiza para móvil"]
    }
  },
  "instagram-stories": {
    name: "Instagram Stories",
    icon: "📱",
    color: "#E4405F",
    promptPrefix: "Create a vertical Instagram Stories ad for: ",
    defaultSize: "1080x1920",
    sizes: ["1080x1920"],
    description: "Crea anuncios verticales para Stories",
    specifications: {
      aspectRatio: "9:16",
      fileSize: "Máximo 30MB",
      fileTypes: ["JPG", "PNG"],
      textLimit: "Sin límite específico",
      guidelines: ["Diseño vertical", "Contenido inmersivo", "Llamada a la acción prominente"]
    }
  },
  google: {
    name: "Google Ads",
    icon: "🔍",
    color: "#4285F4",
    promptPrefix: "Create a Google display ad for: ",
    defaultSize: "300x250",
    sizes: ["300x250", "728x90", "160x600"],
    description: "Crea anuncios display para Google",
    specifications: {
      aspectRatio: "Varios formatos",
      fileSize: "Máximo 150KB",
      fileTypes: ["JPG", "PNG", "GIF"],
      textLimit: "30 caracteres para título",
      guidelines: ["Mensaje claro y directo", "Colores contrastantes", "Logo visible"]
    }
  },
  linkedin: {
    name: "LinkedIn",
    icon: "💼",
    color: "#0A66C2",
    promptPrefix: "Create a professional LinkedIn ad for: ",
    defaultSize: "1080x1080",
    sizes: ["1080x1080", "1200x628"],
    description: "Crea anuncios profesionales para LinkedIn",
    specifications: {
      aspectRatio: "1:1, 16:9",
      fileSize: "Máximo 5MB",
      fileTypes: ["JPG", "PNG"],
      textLimit: "150 caracteres recomendados",
      guidelines: ["Tono profesional", "Valor empresarial claro", "Credibilidad y confianza"]
    }
  },
  youtube: {
    name: "YouTube",
    icon: "📺",
    color: "#FF0000",
    promptPrefix: "Create a YouTube video ad thumbnail for: ",
    defaultSize: "1280x720",
    sizes: ["1280x720"],
    description: "Crea thumbnails para anuncios de YouTube",
    specifications: {
      aspectRatio: "16:9",
      fileSize: "Máximo 2MB",
      fileTypes: ["JPG", "PNG"],
      textLimit: "Sin límite específico",
      guidelines: ["Thumbnail llamativo", "Texto grande y legible", "Colores vibrantes"]
    }
  },
  display: {
    name: "Display Ads",
    icon: "🖼️",
    color: "#6B7280",
    promptPrefix: "Create a display banner ad for: ",
    defaultSize: "728x90",
    sizes: ["728x90", "300x250", "160x600"],
    description: "Crea banners display para sitios web",
    specifications: {
      aspectRatio: "Varios formatos",
      fileSize: "Máximo 150KB",
      fileTypes: ["JPG", "PNG", "GIF"],
      textLimit: "Mínimo texto",
      guidelines: ["Diseño limpio", "Llamada a la acción visible", "Carga rápida"]
    }
  }
};

export default function AdCreatorEditorPage() {
  const [, params] = useRoute("/ad-creator/editor/:platform");
  const platform = params?.platform as PlatformKey;

  // Platform validation
  const validPlatforms: PlatformKey[] = ["facebook", "instagram", "instagram-stories", "google", "linkedin", "youtube", "display"];

  if (!platform || !validPlatforms.includes(platform)) {
    return (
      <DashboardLayout>
        <div className="min-h-screen flex items-center justify-center">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle className="text-red-600">Plataforma no válida</CardTitle>
              <CardDescription>
                La plataforma "{platform}" no está soportada.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button onClick={() => window.history.back()}>
                <ArrowLeft className="w-4 h-4 mr-2" />
                Volver
              </Button>
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    );
  }

  const config = PLATFORM_CONFIGS[platform];

  const handleBack = () => {
    window.history.back();
  };

  return (
    <DashboardLayout>
      <SimplifiedAdCreator
        platform={platform}
        config={config}
        onBack={handleBack}
      />
    </DashboardLayout>
  );
}


