/**
 * Instagram Stories Ad Editor - Specialized editor for Instagram Stories
 */

import React from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Instagram, Wand2 } from "lucide-react";

export default function InstagramStoriesAdEditor() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      {/* Header */}
      <div className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] text-white py-4 px-4 md:px-6">
        <div className="max-w-[1600px] mx-auto">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => window.history.back()}
              className="text-white hover:bg-white/20"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Volver
            </Button>

            <div className="flex items-center gap-3">
              <div className="p-2 bg-white/20 backdrop-blur-sm rounded-lg">
                <Instagram className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-white">
                  Instagram Stories
                </h1>
                <p className="text-sm text-white/80">
                  Stories verticales que capturan la atención
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Contenido principal */}
      <div className="max-w-[1600px] mx-auto px-4 md:px-6 py-8">
        <Card className="max-w-md mx-auto">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Wand2 className="h-5 w-5" />
              En Desarrollo
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 mb-4">
              El editor de Instagram Stories estará disponible próximamente.
            </p>
            <Button onClick={() => window.history.back()}>
              Volver al selector
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
